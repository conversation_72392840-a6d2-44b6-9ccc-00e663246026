{"program": {"fileNames": ["../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es5.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2015.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2016.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2017.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2018.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2019.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2020.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2021.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2022.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.esnext.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.dom.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.dom.iterable.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2015.core.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2017.object.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2017.string.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2017.intl.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2018.intl.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2018.promise.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2019.array.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2019.object.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2019.string.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2019.symbol.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2020.date.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2020.promise.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2020.string.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2020.intl.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2020.number.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2021.promise.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2021.string.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2021.weakref.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2021.intl.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2022.array.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2022.error.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2022.intl.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2022.object.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.es2022.string.d.ts", "../../../../../appdata/roaming/npm/node_modules/typescript/lib/lib.esnext.intl.d.ts", "./.next/types/routes.d.ts", "./next-env.d.ts", "./next.config.ts", "./src/lib/utils.ts", "./src/app/layout.tsx", "./src/app/page.tsx", "./src/components/ui/alert.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/button.tsx", "./src/components/ui/card.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/input.tsx", "./src/components/ui/separator.tsx", "./src/components/ui/sheet.tsx", "./src/components/ui/skeleton.tsx", "./src/components/ui/sonner.tsx", "./src/components/ui/textarea.tsx", "./src/components/ui/tooltip.tsx", "./.next/types/validator.ts"], "fileInfos": [{"version": "f5c28122bee592cfaf5c72ed7bcc47f453b79778ffa6e301f45d21a0970719d4", "affectsGlobalScope": true}, "dc47c4fa66b9b9890cf076304de2a9c5201e94b740cffdf09f87296d877d71f6", "7a387c58583dfca701b6c85e0adaf43fb17d590fb16d5b2dc0a2fbd89f35c467", "8a12173c586e95f4433e0c6dc446bc88346be73ffe9ca6eec7aa63c8f3dca7f9", "5f4e733ced4e129482ae2186aae29fde948ab7182844c3a5a51dd346182c7b06", "e6b724280c694a9f588847f754198fb96c43d805f065c3a5b28bbc9594541c84", "1fc5ab7a764205c68fa10d381b08417795fc73111d6dd16b5b1ed36badb743d9", "746d62152361558ea6d6115cf0da4dd10ede041d14882ede3568bce5dc4b4f1f", "3eb679a56cab01203a1ba7edeade937f6a2a4c718513b2cd930b579807fa9359", "aea179452def8a6152f98f63b191b84e7cbd69b0e248c91e61fb2e52328abe8c", {"version": "3f149f903dd20dfeb7c80e228b659f0e436532de772469980dbd00702cc05cc1", "affectsGlobalScope": true}, {"version": "1272277fe7daa738e555eb6cc45ded42cc2d0f76c07294142283145d49e96186", "affectsGlobalScope": true}, {"version": "adb996790133eb33b33aadb9c09f15c2c575e71fb57a62de8bf74dbf59ec7dfb", "affectsGlobalScope": true}, {"version": "43fb1d932e4966a39a41b464a12a81899d9ae5f2c829063f5571b6b87e6d2f9c", "affectsGlobalScope": true}, {"version": "cdccba9a388c2ee3fd6ad4018c640a471a6c060e96f1232062223063b0a5ac6a", "affectsGlobalScope": true}, {"version": "c5c05907c02476e4bde6b7e76a79ffcd948aedd14b6a8f56e4674221b0417398", "affectsGlobalScope": true}, {"version": "0d5f52b3174bee6edb81260ebcd792692c32c81fd55499d69531496f3f2b25e7", "affectsGlobalScope": true}, {"version": "810627a82ac06fb5166da5ada4159c4ec11978dfbb0805fe804c86406dab8357", "affectsGlobalScope": true}, {"version": "181f1784c6c10b751631b24ce60c7f78b20665db4550b335be179217bacc0d5f", "affectsGlobalScope": true}, {"version": "3013574108c36fd3aaca79764002b3717da09725a36a6fc02eac386593110f93", "affectsGlobalScope": true}, {"version": "75ec0bdd727d887f1b79ed6619412ea72ba3c81d92d0787ccb64bab18d261f14", "affectsGlobalScope": true}, {"version": "3be5a1453daa63e031d266bf342f3943603873d890ab8b9ada95e22389389006", "affectsGlobalScope": true}, {"version": "17bb1fc99591b00515502d264fa55dc8370c45c5298f4a5c2083557dccba5a2a", "affectsGlobalScope": true}, {"version": "7ce9f0bde3307ca1f944119f6365f2d776d281a393b576a18a2f2893a2d75c98", "affectsGlobalScope": true}, {"version": "6a6b173e739a6a99629a8594bfb294cc7329bfb7b227f12e1f7c11bc163b8577", "affectsGlobalScope": true}, {"version": "12a310447c5d23c7d0d5ca2af606e3bd08afda69100166730ab92c62999ebb9d", "affectsGlobalScope": true}, {"version": "b0124885ef82641903d232172577f2ceb5d3e60aed4da1153bab4221e1f6dd4e", "affectsGlobalScope": true}, {"version": "0eb85d6c590b0d577919a79e0084fa1744c1beba6fd0d4e951432fa1ede5510a", "affectsGlobalScope": true}, {"version": "da233fc1c8a377ba9e0bed690a73c290d843c2c3d23a7bd7ec5cd3d7d73ba1e0", "affectsGlobalScope": true}, {"version": "d154ea5bb7f7f9001ed9153e876b2d5b8f5c2bb9ec02b3ae0d239ec769f1f2ae", "affectsGlobalScope": true}, {"version": "bb2d3fb05a1d2ffbca947cc7cbc95d23e1d053d6595391bd325deb265a18d36c", "affectsGlobalScope": true}, {"version": "c80df75850fea5caa2afe43b9949338ce4e2de086f91713e9af1a06f973872b8", "affectsGlobalScope": true}, {"version": "9d57b2b5d15838ed094aa9ff1299eecef40b190722eb619bac4616657a05f951", "affectsGlobalScope": true}, {"version": "6c51b5dd26a2c31dbf37f00cfc32b2aa6a92e19c995aefb5b97a3a64f1ac99de", "affectsGlobalScope": true}, {"version": "6e7997ef61de3132e4d4b2250e75343f487903ddf5370e7ce33cf1b9db9a63ed", "affectsGlobalScope": true}, {"version": "2ad234885a4240522efccd77de6c7d99eecf9b4de0914adb9a35c0c22433f993", "affectsGlobalScope": true}, {"version": "09aa50414b80c023553090e2f53827f007a301bc34b0495bfb2c3c08ab9ad1eb", "affectsGlobalScope": true}, {"version": "d7f680a43f8cd12a6b6122c07c54ba40952b0c8aa140dcfcf32eb9e6cb028596", "affectsGlobalScope": true}, {"version": "3787b83e297de7c315d55d4a7c546ae28e5f6c0a361b7a1dcec1f1f50a54ef11", "affectsGlobalScope": true}, {"version": "e7e8e1d368290e9295ef18ca23f405cf40d5456fa9f20db6373a61ca45f75f40", "affectsGlobalScope": true}, {"version": "faf0221ae0465363c842ce6aa8a0cbda5d9296940a8e26c86e04cc4081eea21e", "affectsGlobalScope": true}, {"version": "06393d13ea207a1bfe08ec8d7be562549c5e2da8983f2ee074e00002629d1871", "affectsGlobalScope": true}, {"version": "cd483c056da900716879771893a3c9772b66c3c88f8943b4205aec738a94b1d0", "affectsGlobalScope": true}, {"version": "b248e32ca52e8f5571390a4142558ae4f203ae2f94d5bac38a3084d529ef4e58", "affectsGlobalScope": true}, {"version": "6c55633c733c8378db65ac3da7a767c3cf2cf3057f0565a9124a16a3a2019e87", "affectsGlobalScope": true}, {"version": "fb4416144c1bf0323ccbc9afb0ab289c07312214e8820ad17d709498c865a3fe", "affectsGlobalScope": true}, {"version": "5b0ca94ec819d68d33da516306c15297acec88efeb0ae9e2b39f71dbd9685ef7", "affectsGlobalScope": true}, {"version": "ff667ee99e5a28c3dc5063a3cfd4d3436699e3fb035d4451037da7f567da542a", "affectsGlobalScope": true}, {"version": "34478567f8a80171f88f2f30808beb7da15eac0538ae91282dd33dce928d98ed", "affectsGlobalScope": true}, {"version": "6ea9ab679ea030cf46c16a711a316078e9e02619ebaf07a7fcd16964aba88f2d", "affectsGlobalScope": true}, {"version": "6bda95ea27a59a276e46043b7065b55bd4b316c25e70e29b572958fa77565d43", "affectsGlobalScope": true}, {"version": "aedb8de1abb2ff1095c153854a6df7deae4a5709c37297f9d6e9948b6806fa66", "affectsGlobalScope": true}, {"version": "11ffe3c281f375fff9ffdde8bbec7669b4dd671905509079f866f2354a788064", "affectsGlobalScope": true}, {"version": "c37f8a49593a0030eecb51bbfa270e709bec9d79a6cc3bb851ef348d4e6b26f8", "affectsGlobalScope": true}, {"version": "a384610388221cd70cffb4503cee7853b8b076f2b4a55324b20a4bdbd25a3538", "affectsGlobalScope": true}, "85ae5aee75f011967cf2d25cbc342f62d69314e9d925f7f4aa3456fc2cffcca6", "614bce25b089c3f19b1e17a6346c74b858034040154c6621e7d35303004767cc", "7c8c3dfc0cdd370d44932828eb067ef771c8fe7996693221d5d4b90af6d54f2d", "e65025ca7842d1b3ec1fcb41768f974cfbb9c5ca85abb2fb2ace6dfa4ac4f860", "5e614fe889099259d8fd3968120b820ddd00f21c65e4d40fe250db8bfc068315", "49d8311589b810b106aada8ef7b13f70b2b1f68cfce4e1458b7f31a191f420e9", "f41c11fc13e9a1f1b849349cb51b137ddd27de0a17c5339675c789d58f556cc4", "7b260b5eeb59e4756fc3907e54852f06d58e8003ff0e030c1d97cff194d87f00", "78cdb63dff3ec7ef074ada630ff20a5d8004768b6cbe24ed134a8bfba12bafbb", "491a87bfae8f28773624d353bf56b9288d0852413e814d508e0d086dc7b06edb", "56b7fd30208addbcc2468cfda4f5bc0a55342211deaab61ee35b1c352b1622f1", "e9dddbd8dbbc0eccd683306481b0f0bbc8e2b60ea97817416ebb9fe7fbbbdca8", "3d6a560c635fd91094e43191ecf73b124ccb2537e6060f85d5d31fcf68150e74", "f2aea81d8baf2b621945382aabac150e3db40f20d19ac1abea7aff805f1dc33c", "958d33cd8a85293728acad21c1f3881da56b805bc11a3ec862d6aa355c152d90", "de4e9fb0fd8e8ed10ea2e475ffe202a922ffe77e3f3edb2c517b0781447ad1b4", "cbc12f4086448f8a307ed5fbb160f79467c84a44c48ca0dbd2c62e39a2db6f5d", "f70e1e9104f989e63e8d2419a4b378ebf3fa951107b8f668ec6a4fbf138c0352"], "options": {"allowSyntheticDefaultImports": true, "esModuleInterop": true, "jsx": 1, "module": 99, "noUncheckedIndexedAccess": true, "skipLibCheck": true, "strict": true, "target": 4}, "fileIdsList": [[55, 59, 60], [55], [58]], "referencedMap": [[73, 1], [56, 2], [61, 3], [62, 3], [63, 3], [64, 3], [65, 3], [66, 3], [67, 3], [68, 3], [69, 3], [71, 3], [72, 3]], "exportedModulesMap": [[73, 1], [56, 2], [61, 3], [62, 3], [63, 3], [64, 3], [65, 3], [66, 3], [67, 3], [68, 3], [69, 3], [71, 3], [72, 3]], "semanticDiagnosticsPerFile": [11, 12, 14, 13, 2, 15, 16, 17, 18, 19, 20, 21, 22, 3, 4, 26, 23, 24, 25, 27, 28, 29, 5, 30, 31, 32, 33, 6, 34, 35, 36, 37, 7, 38, 43, 44, 39, 40, 41, 42, 8, 48, 45, 46, 47, 49, 9, 50, 51, 52, 53, 1, 10, 54, 55, [73, [{"file": "./.next/types/validator.ts", "start": 288, "length": 15, "messageText": "Cannot find module 'next/types.js' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./.next/types/validator.ts", "start": 376, "length": 5, "messageText": "Cannot find namespace 'React'.", "category": 1, "code": 2503}, {"file": "./.next/types/validator.ts", "start": 498, "length": 5, "messageText": "Cannot find namespace 'React'.", "category": 1, "code": 2503}, {"file": "./.next/types/validator.ts", "start": 524, "length": 5, "messageText": "Cannot find namespace 'React'.", "category": 1, "code": 2503}, {"file": "./.next/types/validator.ts", "start": 1042, "length": 5, "messageText": "Cannot find namespace 'React'.", "category": 1, "code": 2503}, {"file": "./.next/types/validator.ts", "start": 1116, "length": 5, "messageText": "Cannot find namespace 'React'.", "category": 1, "code": 2503}, {"file": "./.next/types/validator.ts", "start": 1142, "length": 5, "messageText": "Cannot find namespace 'React'.", "category": 1, "code": 2503}]], 56, [57, [{"file": "./next.config.ts", "start": 32, "length": 6, "messageText": "Cannot find module 'next' or its corresponding type declarations.", "category": 1, "code": 2307}]], [59, [{"file": "./src/app/layout.tsx", "start": 30, "length": 6, "messageText": "Cannot find module 'next' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/app/layout.tsx", "start": 72, "length": 18, "messageText": "Cannot find module 'next/font/google' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/app/layout.tsx", "start": 484, "length": 5, "messageText": "Cannot find namespace 'React'.", "category": 1, "code": 2503}, {"file": "./src/app/layout.tsx", "start": 522, "length": 16, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/layout.tsx", "start": 545, "length": 91, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/layout.tsx", "start": 662, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/layout.tsx", "start": 674, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [60, [{"file": "./src/app/page.tsx", "start": 18, "length": 12, "messageText": "Cannot find module 'next/image' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/app/page.tsx", "start": 81, "length": 130, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/page.tsx", "start": 218, "length": 83, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/page.tsx", "start": 488, "length": 86, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/page.tsx", "start": 585, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/page.tsx", "start": 677, "length": 97, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/page.tsx", "start": 818, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/page.tsx", "start": 850, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/page.tsx", "start": 866, "length": 34, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/page.tsx", "start": 960, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/page.tsx", "start": 974, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/page.tsx", "start": 989, "length": 62, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/page.tsx", "start": 1062, "length": 480, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/page.tsx", "start": 1767, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/page.tsx", "start": 1782, "length": 518, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/page.tsx", "start": 2337, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/page.tsx", "start": 2350, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/page.tsx", "start": 2363, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/page.tsx", "start": 2377, "length": 86, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/page.tsx", "start": 2472, "length": 289, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/page.tsx", "start": 2943, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/page.tsx", "start": 2956, "length": 311, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/page.tsx", "start": 3456, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/page.tsx", "start": 3469, "length": 283, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/page.tsx", "start": 3949, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/page.tsx", "start": 3960, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/app/page.tsx", "start": 3974, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [61, [{"file": "./src/components/ui/alert.tsx", "start": 23, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/components/ui/alert.tsx", "start": 70, "length": 26, "messageText": "Cannot find module 'class-variance-authority' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/components/ui/alert.tsx", "start": 825, "length": 131, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/components/ui/alert.tsx", "start": 1055, "length": 177, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/components/ui/alert.tsx", "start": 1341, "length": 218, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [62, [{"file": "./src/components/ui/badge.tsx", "start": 23, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/components/ui/badge.tsx", "start": 52, "length": 22, "messageText": "Cannot find module '@radix-ui/react-slot' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/components/ui/badge.tsx", "start": 114, "length": 26, "messageText": "Cannot find module 'class-variance-authority' or its corresponding type declarations.", "category": 1, "code": 2307}]], [63, [{"file": "./src/components/ui/button.tsx", "start": 23, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/components/ui/button.tsx", "start": 52, "length": 22, "messageText": "Cannot find module '@radix-ui/react-slot' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/components/ui/button.tsx", "start": 114, "length": 26, "messageText": "Cannot find module 'class-variance-authority' or its corresponding type declarations.", "category": 1, "code": 2307}]], [64, [{"file": "./src/components/ui/card.tsx", "start": 23, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/components/ui/card.tsx", "start": 151, "length": 192, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/components/ui/card.tsx", "start": 442, "length": 272, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/components/ui/card.tsx", "start": 812, "length": 119, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/components/ui/card.tsx", "start": 1035, "length": 128, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/components/ui/card.tsx", "start": 1262, "length": 180, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/components/ui/card.tsx", "start": 1542, "length": 99, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/components/ui/card.tsx", "start": 1740, "length": 133, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [65, [{"file": "./src/components/ui/dialog.tsx", "start": 37, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/components/ui/dialog.tsx", "start": 78, "length": 24, "messageText": "Cannot find module '@radix-ui/react-dialog' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/components/ui/dialog.tsx", "start": 125, "length": 14, "messageText": "Cannot find module 'lucide-react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/components/ui/dialog.tsx", "start": 2583, "length": 26, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/components/ui/dialog.tsx", "start": 2614, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/components/ui/dialog.tsx", "start": 2821, "length": 140, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/components/ui/dialog.tsx", "start": 3062, "length": 174, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [66, [{"file": "./src/components/ui/input.tsx", "start": 23, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/components/ui/input.tsx", "start": 160, "length": 777, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [67, [{"file": "./src/components/ui/separator.tsx", "start": 37, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/components/ui/separator.tsx", "start": 81, "length": 27, "messageText": "Cannot find module '@radix-ui/react-separator' or its corresponding type declarations.", "category": 1, "code": 2307}]], [68, [{"file": "./src/components/ui/sheet.tsx", "start": 37, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/components/ui/sheet.tsx", "start": 77, "length": 24, "messageText": "Cannot find module '@radix-ui/react-dialog' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/components/ui/sheet.tsx", "start": 124, "length": 14, "messageText": "Cannot find module 'lucide-react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/components/ui/sheet.tsx", "start": 2831, "length": 26, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/components/ui/sheet.tsx", "start": 2862, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/components/ui/sheet.tsx", "start": 3052, "length": 120, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"file": "./src/components/ui/sheet.tsx", "start": 3272, "length": 126, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [69, [{"file": "./src/components/ui/skeleton.tsx", "start": 77, "length": 5, "messageText": "Cannot find namespace 'React'.", "category": 1, "code": 2503}, {"file": "./src/components/ui/skeleton.tsx", "start": 123, "length": 125, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [70, [{"file": "./src/components/ui/sonner.tsx", "start": 39, "length": 13, "messageText": "Cannot find module 'next-themes' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/components/ui/sonner.tsx", "start": 101, "length": 8, "messageText": "Cannot find module 'sonner' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/components/ui/sonner.tsx", "start": 486, "length": 5, "messageText": "Cannot find namespace 'React'.", "category": 1, "code": 2503}]], [71, [{"file": "./src/components/ui/textarea.tsx", "start": 23, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/components/ui/textarea.tsx", "start": 160, "length": 571, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [72, [{"file": "./src/components/ui/tooltip.tsx", "start": 37, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/components/ui/tooltip.tsx", "start": 79, "length": 25, "messageText": "Cannot find module '@radix-ui/react-tooltip' or its corresponding type declarations.", "category": 1, "code": 2307}]], [58, [{"file": "./src/lib/utils.ts", "start": 38, "length": 6, "messageText": "Cannot find module 'clsx' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/lib/utils.ts", "start": 69, "length": 16, "messageText": "Cannot find module 'tailwind-merge' or its corresponding type declarations.", "category": 1, "code": 2307}]]], "affectedFilesPendingEmit": [[2, 1], [3, 1], [4, 1], [5, 1], [6, 1], [7, 1], [8, 1], [9, 1], [10, 1], [55, 1], [73, 1], [56, 1], [57, 1], [59, 1], [60, 1], [61, 1], [62, 1], [63, 1], [64, 1], [65, 1], [66, 1], [67, 1], [68, 1], [69, 1], [70, 1], [71, 1], [72, 1], [58, 1]]}, "version": "4.7.3"}